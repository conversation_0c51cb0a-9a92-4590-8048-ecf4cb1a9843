/**
 * Authentication feature exports
 * Following cursor guidelines for feature public API
 *
 * @see .cursor/rules/feature-structure.mdc
 */

// Main page component
export { AuthPage } from './pages/auth-page'

// Individual form components (for potential reuse)
export { ContactForm } from './components/contact-form'
export { OtpForm } from './components/otp-form'
export { PasswordForm } from './components/password-form'
export { SuccessStep } from './components/success-step'

// Store and state management
export { useAuthFlowStore } from './stores/auth-flow.store'

// API hooks
export { useAuthMutations } from './api/mutations'

// Types and schemas
export type {
  ContactInputs,
  OtpInputs,
  PasswordInputs,
  AuthFlowInputs,
} from './models/auth.schema'
export {
  AuthStep,
  AuthAction,
  contactSchema,
  otpSchema,
  passwordSchema,
  authFlowSchema,
  detectContactType,
} from './models/auth.schema'
