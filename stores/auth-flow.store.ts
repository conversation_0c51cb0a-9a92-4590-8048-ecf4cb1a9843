import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { AuthStep, AuthAction, type ContactInputs } from '../models/auth.schema'
import { logger } from '@/shared/lib/logger'

/**
 * Authentication flow state management
 * Using Zustand for cross-component state that survives route changes
 * Following cursor guidelines for state management
 */

interface AuthFlowState {
  // Current state
  step: AuthStep
  action: AuthAction
  contact: string
  contactType: 'phone' | 'email'
  isLoading: boolean
  error: string | null

  // Actions
  setAction: (action: AuthAction) => void
  setContact: (data: ContactInputs) => void
  setStep: (step: AuthStep) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  resetFlow: () => void

  // Flow progression
  goToNextStep: () => void
  goToPreviousStep: () => void
}

/**
 * Initial state for the auth flow
 */
const initialState = {
  step: AuthStep.CONTACT,
  action: AuthAction.LOGIN,
  contact: '',
  contactType: 'email' as const,
  isLoading: false,
  error: null,
}

/**
 * Auth flow store with comprehensive logging
 */
export const useAuthFlowStore = create<AuthFlowState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      /**
       * Set the authentication action (login, signup, forgot password)
       */
      setAction: (action: AuthAction) => {
        logger.info('Auth action changed', {
          previousAction: get().action,
          newAction: action,
        })

        set({
          action,
          step: AuthStep.CONTACT, // Reset to first step when action changes
          error: null,
        })
      },

      /**
       * Set contact information and determine type
       */
      setContact: (data: ContactInputs) => {
        logger.info('Contact information set', {
          contactType: data.contactType,
          contactLength: data.contact.length,
        })

        set({
          contact: data.contact,
          contactType: data.contactType,
          error: null,
        })
      },

      /**
       * Set the current step in the flow
       */
      setStep: (step: AuthStep) => {
        logger.info('Auth step changed', {
          previousStep: get().step,
          newStep: step,
        })

        set({ step, error: null })
      },

      /**
       * Set loading state
       */
      setLoading: (isLoading: boolean) => {
        set({ isLoading })
      },

      /**
       * Set error message
       */
      setError: (error: string | null) => {
        if (error) {
          logger.error('Auth flow error', { error })
        }
        set({ error })
      },

      /**
       * Reset the entire flow to initial state
       */
      resetFlow: () => {
        logger.info('Auth flow reset')
        set(initialState)
      },

      /**
       * Progress to the next step in the flow
       */
      goToNextStep: () => {
        const currentStep = get().step
        const action = get().action

        const stepOrder = [
          AuthStep.CONTACT,
          AuthStep.OTP,
          AuthStep.PASSWORD,
          AuthStep.SUCCESS,
        ]
        const currentIndex = stepOrder.indexOf(currentStep)

        // For login, skip password step if user already exists
        if (action === AuthAction.LOGIN && currentStep === AuthStep.OTP) {
          set({ step: AuthStep.SUCCESS })
          logger.info('Login completed, skipping password step')
          return
        }

        // For signup and forgot password, go through all steps
        if (currentIndex < stepOrder.length - 1) {
          const nextStep = stepOrder[currentIndex + 1]
          logger.info('Progressing to next step', {
            currentStep,
            nextStep,
            action,
          })
          set({ step: nextStep })
        }
      },

      /**
       * Go back to the previous step
       */
      goToPreviousStep: () => {
        const currentStep = get().step
        const stepOrder = [AuthStep.CONTACT, AuthStep.OTP, AuthStep.PASSWORD]
        const currentIndex = stepOrder.indexOf(currentStep)

        if (currentIndex > 0) {
          const previousStep = stepOrder[currentIndex - 1]
          logger.info('Going back to previous step', {
            currentStep,
            previousStep,
          })
          set({ step: previousStep })
        }
      },
    }),
    {
      name: 'auth-flow-store',
    }
  )
)
