import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useTranslation } from 'react-i18next'
import { Button } from '@/shared/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shared/ui/form'
import { Input } from '@/shared/ui/input'
import { Icon } from '@/shared/ui/icon'
import {
  contactSchema,
  type ContactInputs,
  detectContactType,
  AuthAction,
} from '../models/auth.schema'
import { useAuthFlowStore } from '../stores/auth-flow.store'
import { logger } from '@/shared/lib/logger'

/**
 * Contact information form component
 * First step in authentication flow - collects phone/email
 * Following cursor form guidelines with react-hook-form + yup
 *
 * @see .cursor/rules/form-guidelines.mdc
 */
interface ContactFormProps {
  onSubmit: (data: ContactInputs) => Promise<void>
}

export function ContactForm({ onSubmit }: ContactFormProps) {
  const { t } = useTranslation('auth')
  const { action, setAction, isLoading } = useAuthFlowStore()

  /**
   * Form setup with yup validation and cursor guidelines
   */
  const form = useForm<ContactInputs>({
    resolver: yupResolver(contactSchema),
    mode: 'onChange',
    defaultValues: {
      contact: '',
      contactType: 'email',
    },
  })

  /**
   * Handle form submission with contact type detection
   */
  const handleSubmit = async (data: ContactInputs) => {
    // Auto-detect contact type
    const detectedType = detectContactType(data.contact)
    const submitData = {
      ...data,
      contactType: detectedType,
    }

    logger.info('Contact form submitted', {
      contactType: detectedType,
      action,
      contactLength: data.contact.length,
    })

    try {
      await onSubmit(submitData)
    } catch (error) {
      logger.error('Contact form submission failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Get the appropriate icon based on detected contact type
   */
  const getContactIcon = () => {
    const contact = form.watch('contact')
    if (!contact) return Icon.Fi.AtSign

    const type = detectContactType(contact)
    return type === 'email' ? Icon.Fi.Mail : Icon.Fi.Phone
  }

  const ContactIcon = getContactIcon()

  return (
    <div className="w-full max-w-md mx-auto space-y-6 p-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold text-foreground">
          {t('contact-step.title')}
        </h1>
        <p className="text-muted-foreground">{t('contact-step.subtitle')}</p>
      </div>

      {/* Action Selector */}
      <div className="flex rounded-lg border border-border p-1 bg-muted/50">
        {[
          { key: AuthAction.LOGIN, label: t('actions.login') },
          { key: AuthAction.SIGNUP, label: t('actions.signup') },
        ].map((item) => (
          <button
            key={item.key}
            onClick={() => setAction(item.key)}
            className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all ${
              action === item.key
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            {item.label}
          </button>
        ))}
      </div>

      {/* Contact Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="contact"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-foreground">
                  {t('contact-step.contact-label')}
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <ContactIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      {...field}
                      placeholder={t('contact-step.contact-placeholder')}
                      className="pl-10"
                      disabled={isLoading}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={!form.formState.isValid || isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Icon.Fi.Loader className="mr-2 h-4 w-4 animate-spin" />
                {t('common:loading')}
              </>
            ) : (
              t('contact-step.continue')
            )}
          </Button>
        </form>
      </Form>

      {/* Action Switchers */}
      <div className="text-center space-y-2">
        {action === AuthAction.LOGIN ? (
          <>
            <button
              onClick={() => setAction(AuthAction.SIGNUP)}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              {t('contact-step.switch-to-signup')}
            </button>
            <br />
            <button
              onClick={() => setAction(AuthAction.FORGOT_PASSWORD)}
              className="text-sm text-primary hover:text-primary/80 transition-colors"
            >
              {t('contact-step.switch-to-forgot')}
            </button>
          </>
        ) : (
          <button
            onClick={() => setAction(AuthAction.LOGIN)}
            className="text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            {t('contact-step.switch-to-login')}
          </button>
        )}
      </div>
    </div>
  )
}
