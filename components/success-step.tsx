import { useTranslation } from 'react-i18next'
import { But<PERSON> } from '@/shared/ui/button'
import { Icon } from '@/shared/ui/icon'
import { AuthAction } from '../models/auth.schema'
import { useAuthFlowStore } from '../stores/auth-flow.store'
import { logger } from '@/shared/lib/logger'

/**
 * Success step component
 * Final step in authentication flow - shows success message
 * Following cursor guidelines for mobile-first design
 *
 * @see .cursor/rules/mobile-tablet-first.mdc
 */
interface SuccessStepProps {
  onContinue: () => void
}

export function SuccessStep({ onContinue }: SuccessStepProps) {
  const { t } = useTranslation('auth')
  const { action, contact, contactType } = useAuthFlowStore()

  /**
   * Handle continue to dashboard
   */
  const handleContinue = () => {
    logger.info('Auth flow completed successfully', {
      action,
      contactType,
    })

    onContinue()
  }

  /**
   * Get title based on action
   */
  const getTitle = () => {
    switch (action) {
      case AuthAction.LOGIN:
        return t('success-step.login-title')
      case AuthAction.SIGNUP:
        return t('success-step.signup-title')
      case AuthAction.FORGOT_PASSWORD:
        return t('success-step.forgot-title')
      default:
        return t('success-step.login-title')
    }
  }

  /**
   * Get subtitle based on action
   */
  const getSubtitle = () => {
    switch (action) {
      case AuthAction.LOGIN:
        return t('success-step.login-subtitle')
      case AuthAction.SIGNUP:
        return t('success-step.signup-subtitle')
      case AuthAction.FORGOT_PASSWORD:
        return t('success-step.forgot-subtitle')
      default:
        return t('success-step.login-subtitle')
    }
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6 p-6">
      {/* Success Icon */}
      <div className="text-center space-y-4">
        <div className="mx-auto w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-6">
          <Icon.Check className="h-10 w-10 text-green-600 dark:text-green-400" />
        </div>

        <div className="space-y-2">
          <h1 className="text-2xl font-bold text-foreground">{getTitle()}</h1>
          <p className="text-muted-foreground">{getSubtitle()}</p>
        </div>
      </div>

      {/* Contact Info Display */}
      <div className="bg-muted/50 rounded-lg p-4 text-center">
        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
          {contactType === 'email' ? (
            <Icon.Mail className="h-4 w-4" />
          ) : (
            <Icon.Phone className="h-4 w-4" />
          )}
          <span>{contact}</span>
        </div>
      </div>

      {/* Continue Button */}
      <Button onClick={handleContinue} className="w-full" size="lg">
        {t('success-step.continue')}
      </Button>
    </div>
  )
}
