import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useTranslation } from 'react-i18next'
import { useState, useEffect } from 'react'
import { Button } from '@/shared/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shared/ui/form'
import { Input } from '@/shared/ui/input'
import { Icon } from '@/shared/ui/icon'
import { otpSchema, type OtpInputs } from '../models/auth.schema'
import { useAuthFlowStore } from '../stores/auth-flow.store'
import { logger } from '@/shared/lib/logger'

/**
 * OTP verification form component
 * Second step in authentication flow - verifies OTP code
 * Following cursor form guidelines with react-hook-form + yup
 *
 * @see .cursor/rules/form-guidelines.mdc
 */
interface OtpFormProps {
  onSubmit: (data: OtpInputs) => Promise<void>
  onResend: () => Promise<void>
  onBack: () => void
}

export function OtpForm({ onSubmit, onResend, onBack }: OtpFormProps) {
  const { t } = useTranslation('auth')
  const { contact, contactType, isLoading } = useAuthFlowStore()
  const [countdown, setCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)

  /**
   * Form setup with yup validation
   */
  const form = useForm<OtpInputs>({
    resolver: yupResolver(otpSchema),
    mode: 'onChange',
    defaultValues: {
      otp: '',
    },
  })

  /**
   * Countdown timer for resend functionality
   */
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else {
      setCanResend(true)
    }
  }, [countdown])

  /**
   * Handle form submission
   */
  const handleSubmit = async (data: OtpInputs) => {
    logger.info('OTP form submitted', {
      otpLength: data.otp.length,
      contactType,
    })

    try {
      await onSubmit(data)
    } catch (error) {
      logger.error('OTP verification failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Handle resend OTP
   */
  const handleResend = async () => {
    if (!canResend) return

    logger.info('OTP resend requested', { contactType })

    try {
      await onResend()
      setCountdown(60)
      setCanResend(false)
      form.reset()
    } catch (error) {
      logger.error('OTP resend failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }

  /**
   * Mask contact information for display
   */
  const getMaskedContact = () => {
    if (contactType === 'email') {
      const parts = contact.split('@')
      if (parts.length === 2) {
        const localPart = parts[0]
        const domain = parts[1]
        return `${localPart?.slice(0, 2)}***@${domain}`
      }
      return contact // Fallback if split fails
    } else {
      return `***${contact.slice(-4)}`
    }
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6 p-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          {contactType === 'email' ? (
            <Icon.Mail className="h-8 w-8 text-primary" />
          ) : (
            <Icon.Phone className="h-8 w-8 text-primary" />
          )}
        </div>

        <h1 className="text-2xl font-bold text-foreground">
          {t('otp-step.title')}
        </h1>
        <p className="text-muted-foreground">
          {t('otp-step.subtitle')} <br />
          <span className="font-semibold text-foreground">
            {getMaskedContact()}
          </span>
        </p>
      </div>

      {/* OTP Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="otp"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-foreground">
                  {t('otp-step.otp-label')}
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder={t('otp-step.otp-placeholder')}
                    className="text-center text-lg tracking-widest"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={8}
                    disabled={isLoading}
                    autoComplete="one-time-code"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Verify Button */}
          <Button
            type="submit"
            disabled={!form.formState.isValid || isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Icon.Loader className="mr-2 h-4 w-4 animate-spin" />
                {t('common:loading')}
              </>
            ) : (
              t('otp-step.verify')
            )}
          </Button>
        </form>
      </Form>

      {/* Resend Section */}
      <div className="text-center space-y-2">
        {canResend ? (
          <button
            onClick={handleResend}
            className="text-sm text-primary hover:text-primary/80 transition-colors"
          >
            {t('otp-step.resend')}
          </button>
        ) : (
          <p className="text-sm text-muted-foreground">
            {t('otp-step.resend-countdown', { seconds: countdown })}
          </p>
        )}
      </div>

      {/* Back Button */}
      <div className="text-center">
        <button
          onClick={onBack}
          className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          <Icon.ChevronLeft className="h-4 w-4" />
          {t('otp-step.back')}
        </button>
      </div>
    </div>
  )
}
