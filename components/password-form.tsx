import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useTranslation } from 'react-i18next'
import { useState } from 'react'
import { Button } from '@/shared/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shared/ui/form'
import { Input } from '@/shared/ui/input'
import { Icon } from '@/shared/ui/icon'
import {
  passwordSchema,
  type PasswordInputs,
  AuthAction,
} from '../models/auth.schema'
import { useAuthFlowStore } from '../stores/auth-flow.store'
import { logger } from '@/shared/lib/logger'

/**
 * Password creation/reset form component
 * Third step in authentication flow - creates or resets password
 * Following cursor form guidelines with react-hook-form + yup
 *
 * @see .cursor/rules/form-guidelines.mdc
 */
interface PasswordFormProps {
  onSubmit: (data: PasswordInputs) => Promise<void>
  onBack: () => void
}

export function PasswordForm({ onSubmit, onBack }: PasswordFormProps) {
  const { t } = useTranslation('auth')
  const { action, isLoading } = useAuthFlowStore()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  /**
   * Form setup with yup validation
   */
  const form = useForm<PasswordInputs>({
    resolver: yupResolver(passwordSchema),
    mode: 'onChange',
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  /**
   * Handle form submission
   */
  const handleSubmit = async (data: PasswordInputs) => {
    logger.info('Password form submitted', {
      action,
      passwordLength: data.password.length,
    })

    try {
      await onSubmit(data)
    } catch (error) {
      logger.error('Password creation failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        action,
      })
    }
  }

  /**
   * Get form title based on action
   */
  const getTitle = () => {
    return t('password-step.title')
  }

  /**
   * Get submit button text based on action
   */
  const getSubmitText = () => {
    switch (action) {
      case AuthAction.SIGNUP:
        return t('password-step.create-account')
      case AuthAction.FORGOT_PASSWORD:
        return t('password-step.reset-password')
      default:
        return t('password-step.create-account')
    }
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6 p-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <Icon.Fi.Eye className="h-8 w-8 text-primary" />
        </div>

        <h1 className="text-2xl font-bold text-foreground">{getTitle()}</h1>
        <p className="text-muted-foreground">{t('password-step.subtitle')}</p>
      </div>

      {/* Password Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Password Field */}
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-foreground">
                  {t('password-step.password-label')}
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      {...field}
                      type={showPassword ? 'text' : 'password'}
                      placeholder={t('password-step.password-placeholder')}
                      disabled={isLoading}
                      autoComplete="new-password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {showPassword ? (
                        <Icon.EyeOff className="h-4 w-4" />
                      ) : (
                        <Icon.Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Confirm Password Field */}
          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-foreground">
                  {t('password-step.confirm-password-label')}
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      {...field}
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder={t(
                        'password-step.confirm-password-placeholder'
                      )}
                      disabled={isLoading}
                      autoComplete="new-password"
                    />
                    <button
                      type="button"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {showConfirmPassword ? (
                        <Icon.EyeOff className="h-4 w-4" />
                      ) : (
                        <Icon.Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={!form.formState.isValid || isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Icon.Loader className="mr-2 h-4 w-4 animate-spin" />
                {t('common:loading')}
              </>
            ) : (
              getSubmitText()
            )}
          </Button>
        </form>
      </Form>

      {/* Back Button */}
      <div className="text-center">
        <button
          onClick={onBack}
          className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          <Icon.ChevronLeft className="h-4 w-4" />
          {t('password-step.back')}
        </button>
      </div>
    </div>
  )
}
