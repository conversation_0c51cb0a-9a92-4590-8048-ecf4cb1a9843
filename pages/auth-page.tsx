import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { ContactForm } from '../components/contact-form'
import { OtpForm } from '../components/otp-form'
import { PasswordForm } from '../components/password-form'
import { SuccessStep } from '../components/success-step'
import { useAuthFlowStore } from '../stores/auth-flow.store'
import {
  AuthStep,
  type ContactInputs,
  type OtpInputs,
  type PasswordInputs,
} from '../models/auth.schema'
import { useAuthMutations } from '../api/mutations'
import { logger } from '@/shared/lib/logger'

/**
 * Main authentication page component
 * Orchestrates the entire authentication flow with step-by-step progression
 * Following cursor guidelines for feature-first architecture
 *
 * @see .cursor/rules/feature-structure.mdc
 */
export function AuthPage() {
  const navigate = useNavigate()
  const { t } = useTranslation('auth')
  const {
    step,
    action,
    setContact,
    setStep,
    setLoading,
    setError,
    goToNextStep,
    goToPreviousStep,
    resetFlow,
  } = useAuthFlowStore()

  // Auth API mutations
  const {
    sendOtpMutation,
    verifyOtpMutation,
    createPasswordMutation,
    resendOtpMutation,
  } = useAuthMutations()

  /**
   * Handle contact form submission
   * Sends OTP to provided email/phone
   */
  const handleContactSubmit = async (data: ContactInputs) => {
    logger.info('Starting contact submission', {
      contactType: data.contactType,
      action,
    })

    try {
      setLoading(true)
      setContact(data)

      await sendOtpMutation.mutateAsync({
        contact: data.contact,
        contactType: data.contactType,
        action,
      })

      goToNextStep()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      logger.error('Contact submission failed', { error: errorMessage })
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  /**
   * Handle OTP verification
   * Verifies the OTP code and progresses to next step
   */
  const handleOtpSubmit = async (data: OtpInputs) => {
    logger.info('Starting OTP verification', {
      otpLength: data.otp.length,
      action,
    })

    try {
      setLoading(true)

      const result = await verifyOtpMutation.mutateAsync({
        otp: data.otp,
        action,
      })

      // For login, if user exists and OTP is valid, go to success
      // For signup/forgot password, go to password creation
      if (result.requiresPassword) {
        goToNextStep()
      } else {
        setStep(AuthStep.SUCCESS)
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      logger.error('OTP verification failed', { error: errorMessage })
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  /**
   * Handle password creation/reset
   * Creates new password and completes the flow
   */
  const handlePasswordSubmit = async (data: PasswordInputs) => {
    logger.info('Starting password creation', { action })

    try {
      setLoading(true)

      await createPasswordMutation.mutateAsync({
        password: data.password,
        action,
      })

      goToNextStep()
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      logger.error('Password creation failed', { error: errorMessage })
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  /**
   * Handle OTP resend
   */
  const handleOtpResend = async () => {
    logger.info('Resending OTP', { action })

    try {
      await resendOtpMutation.mutateAsync({ action })
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error'
      logger.error('OTP resend failed', { error: errorMessage })
      setError(errorMessage)
    }
  }

  /**
   * Handle successful authentication completion
   * Navigate to dashboard and reset flow
   */
  const handleAuthSuccess = () => {
    logger.info('Authentication completed successfully', { action })
    resetFlow()
    navigate('/dashboard')
  }

  /**
   * Handle back navigation
   */
  const handleBack = () => {
    goToPreviousStep()
  }

  /**
   * Render the appropriate step component
   */
  const renderStep = () => {
    switch (step) {
      case AuthStep.CONTACT:
        return <ContactForm onSubmit={handleContactSubmit} />

      case AuthStep.OTP:
        return (
          <OtpForm
            onSubmit={handleOtpSubmit}
            onResend={handleOtpResend}
            onBack={handleBack}
          />
        )

      case AuthStep.PASSWORD:
        return (
          <PasswordForm onSubmit={handlePasswordSubmit} onBack={handleBack} />
        )

      case AuthStep.SUCCESS:
        return <SuccessStep onContinue={handleAuthSuccess} />

      default:
        return <ContactForm onSubmit={handleContactSubmit} />
    }
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />

      {/* Main Content */}
      <div className="relative w-full max-w-md">
        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex justify-center space-x-2">
            {[
              AuthStep.CONTACT,
              AuthStep.OTP,
              AuthStep.PASSWORD,
              AuthStep.SUCCESS,
            ].map((stepItem, index) => {
              const isActive = step === stepItem
              const isCompleted = Object.values(AuthStep).indexOf(step) > index

              return (
                <div
                  key={stepItem}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    isActive
                      ? 'bg-primary'
                      : isCompleted
                        ? 'bg-primary/60'
                        : 'bg-muted'
                  }`}
                />
              )
            })}
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-card border border-border rounded-lg shadow-lg">
          {renderStep()}
        </div>

        {/* Footer */}
        <div className="mt-6 text-center text-xs text-muted-foreground">
          {t('welcome')} • Mobility Network Dashboard
        </div>
      </div>
    </div>
  )
}

export default AuthPage
