import * as yup from 'yup'

/**
 * Authentication schemas following cursor form guidelines
 * Using yup for validation with Vietnamese and English error messages
 *
 * @see .cursor/rules/form-guidelines.mdc
 */

// Phone number validation regex (international format)
const phoneRegex = /^(\+\d{1,3}[- ]?)?\d{10}$/

// Email validation
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

/**
 * Schema for phone/email input step
 * Validates either phone number or email address
 */
export const contactSchema = yup.object({
  contact: yup
    .string()
    .required(
      'Vui lòng nhập số điện thoại hoặc email / Please enter phone number or email'
    )
    .test(
      'phone-or-email',
      'Định dạng không hợp lệ / Invalid format',
      function (value) {
        if (!value) return false
        return phoneRegex.test(value) || emailRegex.test(value)
      }
    ),
  contactType: yup
    .string()
    .oneOf(['phone', 'email'] as const)
    .required(),
})

/**
 * Schema for OTP verification step
 */
export const otpSchema = yup.object({
  otp: yup
    .string()
    .required('Vui lòng nhập mã OTP / Please enter OTP code')
    .min(
      4,
      'Mã OTP phải có ít nhất 4 ký tự / OTP must be at least 4 characters'
    )
    .max(8, 'Mã OTP không được quá 8 ký tự / OTP must not exceed 8 characters')
    .matches(
      /^\d+$/,
      'Mã OTP chỉ được chứa số / OTP must contain only numbers'
    ),
})

/**
 * Schema for password creation step
 */
export const passwordSchema = yup.object({
  password: yup
    .string()
    .required('Vui lòng nhập mật khẩu / Please enter password')
    .min(
      8,
      'Mật khẩu phải có ít nhất 8 ký tự / Password must be at least 8 characters'
    )
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường và 1 số / Password must contain at least 1 uppercase, 1 lowercase and 1 number'
    ),
  confirmPassword: yup
    .string()
    .required('Vui lòng xác nhận mật khẩu / Please confirm password')
    .oneOf(
      [yup.ref('password')],
      'Mật khẩu không khớp / Passwords do not match'
    ),
})

/**
 * Schema for complete authentication flow
 */
export const authFlowSchema = yup.object({
  contact: yup.string().required(),
  contactType: yup
    .string()
    .oneOf(['phone', 'email'] as const)
    .required(),
  otp: yup.string().required(),
  password: yup.string().required(),
  confirmPassword: yup.string().required(),
})

// TypeScript types derived from schemas
export type ContactInputs = yup.InferType<typeof contactSchema>
export type OtpInputs = yup.InferType<typeof otpSchema>
export type PasswordInputs = yup.InferType<typeof passwordSchema>
export type AuthFlowInputs = yup.InferType<typeof authFlowSchema>

// Auth flow steps enum
export enum AuthStep {
  CONTACT = 'contact',
  OTP = 'otp',
  PASSWORD = 'password',
  SUCCESS = 'success',
}

// Auth action types
export enum AuthAction {
  LOGIN = 'login',
  SIGNUP = 'signup',
  FORGOT_PASSWORD = 'forgot-password',
}

// Contact type detection helper
export function detectContactType(contact: string): 'phone' | 'email' {
  return emailRegex.test(contact) ? 'email' : 'phone'
}
