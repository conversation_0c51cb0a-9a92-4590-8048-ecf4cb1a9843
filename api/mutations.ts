import { useMutation } from '@tanstack/react-query'
import { AuthAction } from '../models/auth.schema'
import { logger } from '@/shared/lib/logger'

/**
 * Authentication API mutations
 * Following cursor guidelines for API layer organization
 *
 * @see .cursor/rules/feature-structure.mdc
 */

// API request types
interface SendOtpRequest {
  contact: string
  contactType: 'phone' | 'email'
  action: AuthAction
}

interface VerifyOtpRequest {
  otp: string
  action: AuthAction
}

interface CreatePasswordRequest {
  password: string
  action: AuthAction
}

interface ResendOtpRequest {
  action: AuthAction
}

// API response types
interface VerifyOtpResponse {
  success: boolean
  requiresPassword: boolean
  token?: string
}

/**
 * Mock API functions - replace with actual API calls
 * These simulate the authentication flow with realistic delays
 */
const authApi = {
  /**
   * Send OTP to email or phone
   */
  async sendOtp(data: SendOtpRequest): Promise<{ success: boolean }> {
    logger.info('API: Sending OTP', {
      contactType: data.contactType,
      action: data.action,
    })

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // Simulate occasional failures for testing
    if (Math.random() < 0.1) {
      throw new Error('Failed to send OTP. Please try again.')
    }

    return { success: true }
  },

  /**
   * Verify OTP code
   */
  async verifyOtp(data: VerifyOtpRequest): Promise<VerifyOtpResponse> {
    logger.info('API: Verifying OTP', {
      otpLength: data.otp.length,
      action: data.action,
    })

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Simulate OTP validation
    if (data.otp !== '123456') {
      throw new Error('Invalid OTP code. Please try again.')
    }

    // For login, check if user exists (simulate)
    const userExists = data.action === AuthAction.LOGIN

    return {
      success: true,
      requiresPassword: !userExists || data.action !== AuthAction.LOGIN,
      token: userExists ? 'mock-jwt-token' : undefined,
    }
  },

  /**
   * Create or reset password
   */
  async createPassword(
    data: CreatePasswordRequest
  ): Promise<{ success: boolean; token: string }> {
    logger.info('API: Creating password', { action: data.action })

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1200))

    // Simulate password validation
    if (data.password.length < 8) {
      throw new Error('Password must be at least 8 characters long.')
    }

    return {
      success: true,
      token: 'mock-jwt-token',
    }
  },

  /**
   * Resend OTP
   */
  async resendOtp(data: ResendOtpRequest): Promise<{ success: boolean }> {
    logger.info('API: Resending OTP', { action: data.action })

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000))

    return { success: true }
  },
}

/**
 * Custom hook for authentication mutations
 * Provides all auth-related mutations with error handling
 */
export function useAuthMutations() {
  /**
   * Send OTP mutation
   */
  const sendOtpMutation = useMutation({
    mutationFn: authApi.sendOtp,
    onSuccess: (_data, variables) => {
      logger.info('OTP sent successfully', {
        contactType: variables.contactType,
        action: variables.action,
      })
    },
    onError: (error, variables) => {
      logger.error('Failed to send OTP', {
        error: error instanceof Error ? error.message : 'Unknown error',
        contactType: variables.contactType,
        action: variables.action,
      })
    },
  })

  /**
   * Verify OTP mutation
   */
  const verifyOtpMutation = useMutation({
    mutationFn: authApi.verifyOtp,
    onSuccess: (data, variables) => {
      logger.info('OTP verified successfully', {
        requiresPassword: data.requiresPassword,
        action: variables.action,
      })
    },
    onError: (error, variables) => {
      logger.error('Failed to verify OTP', {
        error: error instanceof Error ? error.message : 'Unknown error',
        action: variables.action,
      })
    },
  })

  /**
   * Create password mutation
   */
  const createPasswordMutation = useMutation({
    mutationFn: authApi.createPassword,
    onSuccess: (_data, variables) => {
      logger.info('Password created successfully', {
        action: variables.action,
      })
    },
    onError: (error, variables) => {
      logger.error('Failed to create password', {
        error: error instanceof Error ? error.message : 'Unknown error',
        action: variables.action,
      })
    },
  })

  /**
   * Resend OTP mutation
   */
  const resendOtpMutation = useMutation({
    mutationFn: authApi.resendOtp,
    onSuccess: (_data, variables) => {
      logger.info('OTP resent successfully', {
        action: variables.action,
      })
    },
    onError: (error, variables) => {
      logger.error('Failed to resend OTP', {
        error: error instanceof Error ? error.message : 'Unknown error',
        action: variables.action,
      })
    },
  })

  return {
    sendOtpMutation,
    verifyOtpMutation,
    createPasswordMutation,
    resendOtpMutation,
  }
}
